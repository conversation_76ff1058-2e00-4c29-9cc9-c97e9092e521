#!/usr/bin/env python3
"""
Deployment script for Policybazaar Voice Bot
"""

import os
import sys
import subprocess
import json
import argparse
from pathlib import Path

def check_prerequisites():
    """Check if all prerequisites are met."""
    print("🔍 Checking prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print("✅ Python version OK")
    
    # Check credentials file
    if not os.path.exists("matrixteam.json"):
        print("❌ Google credentials file 'matrixteam.json' not found")
        return False
    print("✅ Google credentials found")
    
    # Check if required packages are installed
    try:
        import websockets
        import google.cloud.speech
        import google.cloud.texttospeech
        import google.cloud.aiplatform
        print("✅ Required packages installed")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        return False
    
    return True

def create_systemd_service():
    """Create systemd service file for production deployment."""
    service_content = f"""[Unit]
Description=Policybazaar Voice Bot
After=network.target

[Service]
Type=simple
User=voicebot
WorkingDirectory={os.getcwd()}
Environment=PYTHONPATH={os.getcwd()}
ExecStart={sys.executable} voice_bot_server.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
    
    service_file = "/etc/systemd/system/policybazaar-voicebot.service"
    
    try:
        with open("policybazaar-voicebot.service", "w") as f:
            f.write(service_content)
        
        print(f"✅ Service file created: policybazaar-voicebot.service")
        print(f"To install: sudo cp policybazaar-voicebot.service {service_file}")
        print("Then run: sudo systemctl enable policybazaar-voicebot")
        print("And: sudo systemctl start policybazaar-voicebot")
        
    except Exception as e:
        print(f"❌ Error creating service file: {e}")

def create_nginx_config():
    """Create nginx configuration for WebSocket proxy."""
    nginx_config = """
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain
    
    location / {
        proxy_pass http://127.0.0.1:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific settings
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}

# SSL configuration (recommended for production)
server {
    listen 443 ssl;
    server_name your-domain.com;  # Replace with your domain
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific settings
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}
"""
    
    try:
        with open("nginx-voicebot.conf", "w") as f:
            f.write(nginx_config)
        
        print("✅ Nginx config created: nginx-voicebot.conf")
        print("Copy to: /etc/nginx/sites-available/voicebot")
        print("Enable with: sudo ln -s /etc/nginx/sites-available/voicebot /etc/nginx/sites-enabled/")
        
    except Exception as e:
        print(f"❌ Error creating nginx config: {e}")

def create_docker_files():
    """Create Docker configuration files."""
    
    # Dockerfile
    dockerfile_content = """FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    portaudio19-dev \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 voicebot && chown -R voicebot:voicebot /app
USER voicebot

# Expose port
EXPOSE 8765

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD python -c "import asyncio, websockets; asyncio.run(websockets.connect('ws://localhost:8765'))"

# Start the application
CMD ["python", "voice_bot_server.py"]
"""
    
    # Docker Compose
    docker_compose_content = """version: '3.8'

services:
  voicebot:
    build: .
    ports:
      - "8765:8765"
    volumes:
      - ./matrixteam.json:/app/matrixteam.json:ro
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import asyncio, websockets; asyncio.run(websockets.connect('ws://localhost:8765'))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-voicebot.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # Mount SSL certificates if available
    depends_on:
      - voicebot
    restart: unless-stopped
"""
    
    try:
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content)
        
        with open("docker-compose.yml", "w") as f:
            f.write(docker_compose_content)
        
        print("✅ Docker files created: Dockerfile, docker-compose.yml")
        print("To deploy: docker-compose up -d")
        
    except Exception as e:
        print(f"❌ Error creating Docker files: {e}")

def create_monitoring_script():
    """Create monitoring script for production."""
    monitoring_script = """#!/bin/bash

# Policybazaar Voice Bot Monitoring Script

LOG_FILE="/var/log/voicebot-monitor.log"
SERVICE_NAME="policybazaar-voicebot"
WEBHOOK_URL=""  # Add your Slack/Teams webhook URL

log_message() {
    echo "$(date): $1" >> $LOG_FILE
}

check_service() {
    if systemctl is-active --quiet $SERVICE_NAME; then
        return 0
    else
        return 1
    fi
}

check_websocket() {
    python3 -c "
import asyncio
import websockets
import sys

async def test_connection():
    try:
        async with websockets.connect('ws://localhost:8765') as websocket:
            await websocket.send('{\"type\": \"ping\"}')
            return True
    except:
        return False

result = asyncio.run(test_connection())
sys.exit(0 if result else 1)
"
}

send_alert() {
    local message="$1"
    log_message "ALERT: $message"
    
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \\
            --data "{\"text\":\"🚨 Voice Bot Alert: $message\"}" \\
            "$WEBHOOK_URL"
    fi
}

# Main monitoring loop
if ! check_service; then
    send_alert "Service $SERVICE_NAME is not running"
    systemctl start $SERVICE_NAME
    sleep 10
fi

if ! check_websocket; then
    send_alert "WebSocket endpoint is not responding"
    systemctl restart $SERVICE_NAME
fi

log_message "Health check completed"
"""
    
    try:
        with open("monitor.sh", "w") as f:
            f.write(monitoring_script)
        
        os.chmod("monitor.sh", 0o755)
        
        print("✅ Monitoring script created: monitor.sh")
        print("Add to crontab: */5 * * * * /path/to/monitor.sh")
        
    except Exception as e:
        print(f"❌ Error creating monitoring script: {e}")

def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description="Deploy Policybazaar Voice Bot")
    parser.add_argument("--type", choices=["systemd", "docker", "all"], 
                       default="all", help="Deployment type")
    
    args = parser.parse_args()
    
    print("🚀 Policybazaar Voice Bot Deployment")
    print("=" * 50)
    
    if not check_prerequisites():
        print("❌ Prerequisites not met. Please fix the issues above.")
        sys.exit(1)
    
    if args.type in ["systemd", "all"]:
        print("\n📦 Creating systemd service...")
        create_systemd_service()
    
    if args.type in ["docker", "all"]:
        print("\n🐳 Creating Docker configuration...")
        create_docker_files()
    
    if args.type in ["all"]:
        print("\n🌐 Creating nginx configuration...")
        create_nginx_config()
        
        print("\n📊 Creating monitoring script...")
        create_monitoring_script()
    
    print("\n" + "=" * 50)
    print("✅ Deployment files created successfully!")
    print("\nNext steps:")
    print("1. Review and customize the generated configuration files")
    print("2. Set up SSL certificates for production")
    print("3. Configure monitoring and alerting")
    print("4. Test the deployment in a staging environment")
    print("5. Deploy to production")

if __name__ == "__main__":
    main()
