#!/usr/bin/env python3
"""
Performance monitoring script for Policybazaar Voice Bot
"""

import asyncio
import websockets
import json
import time
import statistics
import base64
import wave
import numpy as np
from typing import List, Dict, Any
import config

class PerformanceMonitor:
    """Monitor voice bot performance and latency."""
    
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
        self.test_audio_data = self.generate_test_audio()
    
    def generate_test_audio(self) -> bytes:
        """Generate test audio data."""
        # Generate 2 seconds of sine wave
        duration = 2.0
        sample_rate = config.SAMPLE_RATE
        frequency = 440  # A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # Convert to 16-bit integers
        audio_data = (audio_data * 32767).astype(np.int16)
        return audio_data.tobytes()
    
    async def test_single_request(self, test_id: int) -> Dict[str, Any]:
        """Test a single request-response cycle."""
        start_time = time.time()
        
        try:
            # Connect to server
            uri = f"ws://{config.HOST}:{config.PORT}"
            async with websockets.connect(uri) as websocket:
                connect_time = time.time()
                
                # Send audio data
                audio_base64 = base64.b64encode(self.test_audio_data).decode('utf-8')
                message = {
                    "type": "audio",
                    "audio": audio_base64
                }
                
                await websocket.send(json.dumps(message))
                send_time = time.time()
                
                # Wait for response
                response = await websocket.recv()
                receive_time = time.time()
                
                # Parse response
                data = json.loads(response)
                
                # Send end call
                await websocket.send(json.dumps({"type": "end_call"}))
                end_time = time.time()
                
                return {
                    "test_id": test_id,
                    "success": True,
                    "total_time": end_time - start_time,
                    "connect_time": connect_time - start_time,
                    "send_time": send_time - connect_time,
                    "response_time": receive_time - send_time,
                    "cleanup_time": end_time - receive_time,
                    "response_text": data.get("text", ""),
                    "has_audio": "audio" in data,
                    "timestamp": start_time
                }
                
        except Exception as e:
            end_time = time.time()
            return {
                "test_id": test_id,
                "success": False,
                "total_time": end_time - start_time,
                "error": str(e),
                "timestamp": start_time
            }
    
    async def run_performance_test(self, num_tests: int = 10, concurrent: int = 1):
        """Run performance tests."""
        print(f"🚀 Starting performance test with {num_tests} requests, {concurrent} concurrent")
        print("=" * 60)
        
        if concurrent == 1:
            # Sequential tests
            for i in range(num_tests):
                print(f"Running test {i+1}/{num_tests}...", end=" ")
                result = await self.test_single_request(i+1)
                self.results.append(result)
                
                if result["success"]:
                    print(f"✅ {result['total_time']:.2f}s")
                else:
                    print(f"❌ {result['error']}")
                
                # Small delay between tests
                await asyncio.sleep(1)
        else:
            # Concurrent tests
            print(f"Running {num_tests} concurrent tests...")
            tasks = []
            for i in range(num_tests):
                task = asyncio.create_task(self.test_single_request(i+1))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.results.append({
                        "test_id": i+1,
                        "success": False,
                        "error": str(result),
                        "timestamp": time.time()
                    })
                else:
                    self.results.append(result)
        
        self.analyze_results()
    
    def analyze_results(self):
        """Analyze and display test results."""
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE ANALYSIS")
        print("=" * 60)
        
        successful_tests = [r for r in self.results if r["success"]]
        failed_tests = [r for r in self.results if not r["success"]]
        
        print(f"Total Tests: {len(self.results)}")
        print(f"Successful: {len(successful_tests)} ({len(successful_tests)/len(self.results)*100:.1f}%)")
        print(f"Failed: {len(failed_tests)} ({len(failed_tests)/len(self.results)*100:.1f}%)")
        
        if successful_tests:
            # Timing analysis
            total_times = [r["total_time"] for r in successful_tests]
            response_times = [r["response_time"] for r in successful_tests]
            
            print(f"\n⏱️  TIMING ANALYSIS")
            print(f"Average Total Time: {statistics.mean(total_times):.3f}s")
            print(f"Median Total Time: {statistics.median(total_times):.3f}s")
            print(f"Min Total Time: {min(total_times):.3f}s")
            print(f"Max Total Time: {max(total_times):.3f}s")
            print(f"Std Dev: {statistics.stdev(total_times):.3f}s")
            
            print(f"\n🎯 RESPONSE TIME ANALYSIS")
            print(f"Average Response Time: {statistics.mean(response_times):.3f}s")
            print(f"Median Response Time: {statistics.median(response_times):.3f}s")
            print(f"Min Response Time: {min(response_times):.3f}s")
            print(f"Max Response Time: {max(response_times):.3f}s")
            
            # Latency categories
            fast_responses = len([t for t in response_times if t < 1.0])
            medium_responses = len([t for t in response_times if 1.0 <= t < 2.0])
            slow_responses = len([t for t in response_times if t >= 2.0])
            
            print(f"\n📈 LATENCY DISTRIBUTION")
            print(f"Fast (<1s): {fast_responses} ({fast_responses/len(successful_tests)*100:.1f}%)")
            print(f"Medium (1-2s): {medium_responses} ({medium_responses/len(successful_tests)*100:.1f}%)")
            print(f"Slow (>2s): {slow_responses} ({slow_responses/len(successful_tests)*100:.1f}%)")
            
            # Response quality
            responses_with_audio = len([r for r in successful_tests if r.get("has_audio", False)])
            print(f"\n🔊 RESPONSE QUALITY")
            print(f"Responses with Audio: {responses_with_audio}/{len(successful_tests)}")
            
            # Sample responses
            print(f"\n💬 SAMPLE RESPONSES")
            for i, result in enumerate(successful_tests[:3]):
                text = result.get("response_text", "No text")[:50]
                print(f"  {i+1}. {text}{'...' if len(result.get('response_text', '')) > 50 else ''}")
        
        if failed_tests:
            print(f"\n❌ FAILURE ANALYSIS")
            error_counts = {}
            for test in failed_tests:
                error = test.get("error", "Unknown error")
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"  {error}: {count} occurrences")
        
        print("\n" + "=" * 60)
    
    def save_results(self, filename: str = "performance_results.json"):
        """Save results to JSON file."""
        with open(filename, 'w') as f:
            json.dump({
                "timestamp": time.time(),
                "total_tests": len(self.results),
                "successful_tests": len([r for r in self.results if r["success"]]),
                "results": self.results
            }, f, indent=2)
        print(f"📁 Results saved to {filename}")

async def main():
    """Main function."""
    monitor = PerformanceMonitor()
    
    print("Policybazaar Voice Bot - Performance Monitor")
    print("=" * 50)
    
    try:
        # Test server connectivity first
        print("Testing server connectivity...")
        uri = f"ws://{config.HOST}:{config.PORT}"
        async with websockets.connect(uri) as websocket:
            await websocket.send(json.dumps({"type": "ping"}))
            print("✅ Server is reachable")
        
        # Run performance tests
        await monitor.run_performance_test(num_tests=5, concurrent=1)
        
        # Save results
        monitor.save_results()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure the voice bot server is running: python voice_bot_server.py")

if __name__ == "__main__":
    asyncio.run(main())
