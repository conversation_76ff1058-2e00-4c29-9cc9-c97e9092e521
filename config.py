import os
import json
from google.oauth2 import service_account

# Google Cloud Configuration
GOOGLE_CREDENTIALS_PATH = "matrixteam.json"
PROJECT_ID = "named-defender-182511"  # Update this with your actual project ID

# Audio Configuration
SAMPLE_RATE = 16000
CHUNK_SIZE = 1024
AUDIO_FORMAT = "LINEAR16"
LANGUAGE_CODE = "en-IN"  # Indian English for Policybazaar

# WebSocket Configuration
HOST = "localhost"
PORT = 8765

# Vertex AI Configuration
VERTEX_AI_MODEL = "gemini-1.5-flash"
VERTEX_AI_LOCATION = "asia-south1"

# Text-to-Speech Configuration
TTS_VOICE_NAME = "en-IN-Wavenet-A"  # Indian English voice
TTS_LANGUAGE_CODE = "en-IN"

def get_credentials():
    """Load Google Cloud credentials from service account file."""
    return service_account.Credentials.from_service_account_file(
        GOOGLE_CREDENTIALS_PATH,
        scopes=[
            "https://www.googleapis.com/auth/cloud-platform",
            "https://www.googleapis.com/auth/speech",
            "https://www.googleapis.com/auth/texttospeech"
        ]
    )

def get_project_info():
    """Get project ID and project number from credentials file."""
    try:
        with open(GOOGLE_CREDENTIALS_PATH, 'r') as f:
            creds_data = json.load(f)

        project_id = creds_data.get('project_id', PROJECT_ID)

        # Try to get project number from Google Cloud
        try:
            import subprocess
            result = subprocess.run(
                f"gcloud projects describe {project_id} --format='value(projectNumber)'",
                shell=True, capture_output=True, text=True
            )
            if result.returncode == 0:
                project_number = result.stdout.strip()
                return project_id, project_number
        except:
            pass

        return project_id, None

    except Exception as e:
        print(f"Warning: Could not read credentials file: {e}")
        return PROJECT_ID, None
