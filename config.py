import os
from google.oauth2 import service_account

# Google Cloud Configuration
GOOGLE_CREDENTIALS_PATH = "matrixteam.json"
PROJECT_ID = "named-defender-182511"  # Update this with your actual project ID

# Audio Configuration
SAMPLE_RATE = 16000
CHUNK_SIZE = 1024
AUDIO_FORMAT = "LINEAR16"
LANGUAGE_CODE = "en-IN"  # Indian English for Policybazaar

# WebSocket Configuration
HOST = "localhost"
PORT = 8765

# Vertex AI Configuration
VERTEX_AI_MODEL = "gemini-1.5-flash"
VERTEX_AI_LOCATION = "asia-south1"

# Text-to-Speech Configuration
TTS_VOICE_NAME = "en-IN-Wavenet-A"  # Indian English voice
TTS_LANGUAGE_CODE = "en-IN"

def get_credentials():
    """Load Google Cloud credentials from service account file."""
    return service_account.Credentials.from_service_account_file(
        GOOGLE_CREDENTIALS_PATH,
        scopes=[
            "https://www.googleapis.com/auth/cloud-platform",
            "https://www.googleapis.com/auth/speech",
            "https://www.googleapis.com/auth/texttospeech"
        ]
    )
