import asyncio
import websockets
import json
import logging
import base64
import time
from typing import Optional
from google.cloud import speech
from google.cloud import texttospeech
from google.cloud import aiplatform
import config
from error_handler import (
    voice_bot_logger,
    handle_exceptions,
    speech_recognition_breaker,
    tts_breaker,
    ai_generation_breaker
)

class VoiceBotServer:
    def __init__(self):
        self.credentials = config.get_credentials()
        self.speech_client = speech.SpeechClient(credentials=self.credentials)
        # TTS temporarily disabled - uncomment when permissions are added
        # self.tts_client = texttospeech.TextToSpeechClient(credentials=self.credentials)
        
        # Initialize Vertex AI
        aiplatform.init(
            project=config.PROJECT_ID,
            location=config.VERTEX_AI_LOCATION,
            credentials=self.credentials
        )
        
        # Speech recognition configuration
        self.speech_config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=config.SAMPLE_RATE,
            language_code=config.LANGUAGE_CODE,
            enable_automatic_punctuation=True,
            model="phone_call"  # Optimized for phone calls
        )
        
        # TTS configuration - temporarily disabled
        # self.tts_voice = texttospeech.VoiceSelectionParams(
        #     language_code=config.TTS_LANGUAGE_CODE,
        #     name=config.TTS_VOICE_NAME
        # )
        #
        # self.tts_audio_config = texttospeech.AudioConfig(
        #     audio_encoding=texttospeech.AudioEncoding.LINEAR16,
        #     sample_rate_hertz=config.SAMPLE_RATE
        # )
        
        # Active connections
        self.connections = set()

    @handle_exceptions("websocket")
    async def handle_client(self, websocket, path):
        """Handle incoming WebSocket connections from dialer team."""
        self.connections.add(websocket)
        client_info = {"address": str(websocket.remote_address), "timestamp": time.time()}
        voice_bot_logger.log_call_start(client_info)

        call_start_time = time.time()
        call_success = False

        try:
            # Initialize streaming recognition
            streaming_config = speech.StreamingRecognitionConfig(
                config=self.speech_config,
                interim_results=True,
                single_utterance=False
            )
            
            # Create a queue for audio chunks
            audio_queue = asyncio.Queue()
            
            # Start the speech recognition task
            recognition_task = asyncio.create_task(
                self.stream_recognition(websocket, streaming_config, audio_queue)
            )
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get("type") == "audio":
                        # Decode base64 audio data
                        audio_data = base64.b64decode(data["audio"])
                        await audio_queue.put(audio_data)
                        
                    elif data.get("type") == "end_call":
                        voice_bot_logger.logger.info("Call ended by client")
                        call_success = True
                        break

                except json.JSONDecodeError:
                    voice_bot_logger.logger.error("Invalid JSON received")
                except Exception as e:
                    voice_bot_logger.logger.error(f"Error processing message: {e}")

            # Cancel recognition task
            recognition_task.cancel()
            call_success = True

        except websockets.exceptions.ConnectionClosed:
            voice_bot_logger.logger.info("Client disconnected")
            call_success = True
        except Exception as e:
            voice_bot_logger.logger.error(f"Error handling client: {e}")
            call_success = False
        finally:
            call_duration = time.time() - call_start_time
            voice_bot_logger.log_call_end(call_success, call_duration, None if call_success else str(e) if 'e' in locals() else "Unknown error")
            self.connections.discard(websocket)

    @handle_exceptions("speech_recognition")
    async def stream_recognition(self, websocket, streaming_config, audio_queue):
        """Handle streaming speech recognition."""
        try:
            # Create streaming recognition request generator
            async def request_generator():
                # First request with configuration
                yield speech.StreamingRecognizeRequest(
                    streaming_config=streaming_config
                )
                
                # Subsequent requests with audio data
                while True:
                    try:
                        audio_data = await asyncio.wait_for(audio_queue.get(), timeout=1.0)
                        yield speech.StreamingRecognizeRequest(audio_content=audio_data)
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        voice_bot_logger.logger.error(f"Error in request generator: {e}")
                        break

            # Start streaming recognition with circuit breaker
            responses = speech_recognition_breaker.call(
                self.speech_client.streaming_recognize,
                request_generator()
            )
            
            for response in responses:
                if not response.results:
                    continue
                
                result = response.results[0]
                if not result.alternatives:
                    continue
                
                transcript = result.alternatives[0].transcript
                
                # Process final results
                if result.is_final:
                    voice_bot_logger.logger.info(f"Final transcript: {transcript}")

                    # Generate AI response
                    ai_response = await self.generate_ai_response(transcript)

                    # Send text response (TTS temporarily disabled)
                    await self.send_text_response(websocket, ai_response, transcript)

        except Exception as e:
            voice_bot_logger.log_speech_recognition_error(e)
            # Send fallback response
            await self.send_text_response(websocket, "I'm sorry, I didn't catch that. Could you please repeat?", "")

    @handle_exceptions("ai_generation")
    async def generate_ai_response(self, user_input: str) -> str:
        """Generate AI response using Vertex AI."""
        try:
            # Import Vertex AI Generative AI
            from vertexai.generative_models import GenerativeModel
            
            model = GenerativeModel(config.VERTEX_AI_MODEL)
            
            # Create a context-aware prompt for Policybazaar
            prompt = f"""
            You are a helpful customer service representative for Policybazaar, India's leading insurance marketplace. 
            You help customers with insurance queries, policy information, and general assistance.
            
            Keep responses:
            - Brief and conversational (1-2 sentences max)
            - Helpful and professional
            - Focused on insurance-related topics
            
            Customer said: "{user_input}"
            
            Respond naturally:
            """
            
            response = ai_generation_breaker.call(model.generate_content, prompt)
            return response.text.strip()

        except Exception as e:
            voice_bot_logger.log_ai_generation_error(e)
            return "I apologize, but I'm having trouble processing your request right now. How else can I help you with your insurance needs?"

    async def send_text_response(self, websocket, ai_response: str, user_transcript: str):
        """Send text response back to client (TTS disabled temporarily)."""
        try:
            response_data = {
                "type": "response",
                "text": ai_response,
                "user_input": user_transcript,
                "timestamp": time.time(),
                "audio": None  # No audio for now
            }

            await websocket.send(json.dumps(response_data))
            voice_bot_logger.logger.info(f"Text response sent: {ai_response[:100]}...")

        except Exception as e:
            voice_bot_logger.logger.error(f"Error sending text response: {e}")

    # TTS method temporarily disabled - uncomment when permissions are added
    # @handle_exceptions("tts")
    async def send_audio_response(self, websocket, text_response: str):
        """Convert text to speech and send audio response."""
        # TTS temporarily disabled - uncomment when permissions are added
        # try:
        #     # Generate speech with circuit breaker
        #     synthesis_input = texttospeech.SynthesisInput(text=text_response)
        #
        #     response = tts_breaker.call(
        #         self.tts_client.synthesize_speech,
        #         input=synthesis_input,
        #         voice=self.tts_voice,
        #         audio_config=self.tts_audio_config
        #     )
        #
        #     # Encode audio as base64
        #     audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')
        #
        #     # Send response back to client
        #     response_data = {
        #         "type": "audio_response",
        #         "audio": audio_base64,
        #         "text": text_response
        #     }
        #
        #     await websocket.send(json.dumps(response_data))
        #     voice_bot_logger.logger.info(f"Sent audio response: {text_response}")
        #
        # except Exception as e:
        #     voice_bot_logger.log_tts_error(e)

        # For now, send text response instead
        await self.send_text_response(websocket, text_response, "")

    async def start_server(self):
        """Start the WebSocket server."""
        voice_bot_logger.logger.info(f"Starting voice bot server on {config.HOST}:{config.PORT}")

        # Log metrics periodically
        async def log_metrics_periodically():
            while True:
                await asyncio.sleep(300)  # Log every 5 minutes
                voice_bot_logger.log_metrics()

        # Start metrics logging task
        metrics_task = asyncio.create_task(log_metrics_periodically())

        try:
            async with websockets.serve(
                self.handle_client,
                config.HOST,
                config.PORT,
                ping_interval=20,
                ping_timeout=10
            ):
                voice_bot_logger.logger.info("Voice bot server is running...")
                await asyncio.Future()  # Run forever
        finally:
            metrics_task.cancel()

if __name__ == "__main__":
    bot = VoiceBotServer()
    asyncio.run(bot.start_server())
