import logging
import traceback
import json
import time
from typing import Dict, Any, Optional
from functools import wraps
import asyncio

class VoiceBotLogger:
    """Enhanced logging for voice bot operations."""
    
    def __init__(self, name: str = "voice_bot"):
        self.logger = logging.getLogger(name)
        self.setup_logging()
        
        # Metrics tracking
        self.metrics = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "avg_response_time": 0.0,
            "total_response_time": 0.0,
            "speech_recognition_errors": 0,
            "tts_errors": 0,
            "ai_generation_errors": 0,
            "websocket_errors": 0
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler('voice_bot.log')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # Error file handler
        error_handler = logging.FileHandler('voice_bot_errors.log')
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        
        # Add handlers
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def log_call_start(self, client_info: Dict[str, Any]):
        """Log the start of a new call."""
        self.metrics["total_calls"] += 1
        self.logger.info(f"New call started - Client: {client_info}")
    
    def log_call_end(self, success: bool, duration: float, error: Optional[str] = None):
        """Log the end of a call."""
        if success:
            self.metrics["successful_calls"] += 1
            self.logger.info(f"Call completed successfully - Duration: {duration:.2f}s")
        else:
            self.metrics["failed_calls"] += 1
            self.logger.error(f"Call failed - Duration: {duration:.2f}s - Error: {error}")
        
        # Update response time metrics
        self.metrics["total_response_time"] += duration
        self.metrics["avg_response_time"] = (
            self.metrics["total_response_time"] / self.metrics["total_calls"]
        )
    
    def log_speech_recognition_error(self, error: Exception):
        """Log speech recognition errors."""
        self.metrics["speech_recognition_errors"] += 1
        self.logger.error(f"Speech recognition error: {error}")
        self.logger.debug(traceback.format_exc())
    
    def log_tts_error(self, error: Exception):
        """Log text-to-speech errors."""
        self.metrics["tts_errors"] += 1
        self.logger.error(f"TTS error: {error}")
        self.logger.debug(traceback.format_exc())
    
    def log_ai_generation_error(self, error: Exception):
        """Log AI generation errors."""
        self.metrics["ai_generation_errors"] += 1
        self.logger.error(f"AI generation error: {error}")
        self.logger.debug(traceback.format_exc())
    
    def log_websocket_error(self, error: Exception):
        """Log WebSocket errors."""
        self.metrics["websocket_errors"] += 1
        self.logger.error(f"WebSocket error: {error}")
        self.logger.debug(traceback.format_exc())
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        return self.metrics.copy()
    
    def log_metrics(self):
        """Log current metrics."""
        self.logger.info(f"Voice Bot Metrics: {json.dumps(self.metrics, indent=2)}")

# Global logger instance
voice_bot_logger = VoiceBotLogger()

def handle_exceptions(error_type: str = "general"):
    """Decorator for handling exceptions in voice bot functions."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Log based on error type
                if error_type == "speech_recognition":
                    voice_bot_logger.log_speech_recognition_error(e)
                elif error_type == "tts":
                    voice_bot_logger.log_tts_error(e)
                elif error_type == "ai_generation":
                    voice_bot_logger.log_ai_generation_error(e)
                elif error_type == "websocket":
                    voice_bot_logger.log_websocket_error(e)
                else:
                    voice_bot_logger.logger.error(f"Unhandled error in {func.__name__}: {e}")
                    voice_bot_logger.logger.debug(traceback.format_exc())
                
                # Re-raise for handling at higher level
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log based on error type
                if error_type == "speech_recognition":
                    voice_bot_logger.log_speech_recognition_error(e)
                elif error_type == "tts":
                    voice_bot_logger.log_tts_error(e)
                elif error_type == "ai_generation":
                    voice_bot_logger.log_ai_generation_error(e)
                elif error_type == "websocket":
                    voice_bot_logger.log_websocket_error(e)
                else:
                    voice_bot_logger.logger.error(f"Unhandled error in {func.__name__}: {e}")
                    voice_bot_logger.logger.debug(traceback.format_exc())
                
                # Re-raise for handling at higher level
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class CircuitBreaker:
    """Circuit breaker pattern for handling service failures."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                voice_bot_logger.logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN - service unavailable")
        
        try:
            result = func(*args, **kwargs)
            
            # Success - reset failure count
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                voice_bot_logger.logger.info("Circuit breaker reset to CLOSED state")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                voice_bot_logger.logger.error(
                    f"Circuit breaker opened after {self.failure_count} failures"
                )
            
            raise

# Global circuit breakers for different services
speech_recognition_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=30)
tts_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=30)
ai_generation_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60)
