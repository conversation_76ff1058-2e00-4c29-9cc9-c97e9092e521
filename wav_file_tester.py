#!/usr/bin/env python3
"""
WAV file tester for Policybazaar Voice Bot
Process WAV files and get audio responses
"""

import asyncio
import websockets
import json
import base64
import wave
import argparse
import logging
import os
from pathlib import Path
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WAVFileTester:
    def __init__(self):
        self.websocket = None
    
    async def connect(self):
        """Connect to the voice bot server."""
        try:
            uri = f"ws://{config.HOST}:{config.PORT}"
            self.websocket = await websockets.connect(uri)
            logger.info(f"Connected to voice bot at {uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the server."""
        if self.websocket:
            try:
                await self.websocket.send(json.dumps({"type": "end_call"}))
                await self.websocket.close()
                logger.info("Disconnected from voice bot")
            except:
                pass
    
    def validate_wav_file(self, file_path: str) -> bool:
        """Validate WAV file format."""
        try:
            with wave.open(file_path, 'rb') as wf:
                # Check audio format
                channels = wf.getnchannels()
                sample_width = wf.getsampwidth()
                framerate = wf.getframerate()
                
                logger.info(f"WAV file info:")
                logger.info(f"  Channels: {channels}")
                logger.info(f"  Sample width: {sample_width} bytes")
                logger.info(f"  Frame rate: {framerate} Hz")
                logger.info(f"  Duration: {wf.getnframes() / framerate:.2f} seconds")
                
                # Warn about non-optimal formats
                if channels != 1:
                    logger.warning(f"Audio has {channels} channels. Mono (1 channel) is recommended.")
                
                if sample_width != 2:
                    logger.warning(f"Audio is {sample_width * 8}-bit. 16-bit is recommended.")
                
                if framerate != config.SAMPLE_RATE:
                    logger.warning(f"Audio is {framerate}Hz. {config.SAMPLE_RATE}Hz is recommended.")
                
                return True
                
        except Exception as e:
            logger.error(f"Invalid WAV file: {e}")
            return False
    
    async def process_wav_file(self, file_path: str, chunk_size: int = 1024) -> dict:
        """Process a WAV file and get response from voice bot."""
        if not os.path.exists(file_path):
            return {"error": f"File not found: {file_path}"}
        
        if not self.validate_wav_file(file_path):
            return {"error": "Invalid WAV file format"}
        
        try:
            logger.info(f"Processing WAV file: {file_path}")
            
            with wave.open(file_path, 'rb') as wf:
                # Send audio in chunks to simulate streaming
                total_frames = wf.getnframes()
                frames_sent = 0
                
                while frames_sent < total_frames:
                    # Read chunk
                    frames_to_read = min(chunk_size, total_frames - frames_sent)
                    audio_data = wf.readframes(frames_to_read)
                    
                    if not audio_data:
                        break
                    
                    # Send to voice bot
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    message = {
                        "type": "audio",
                        "audio": audio_base64
                    }
                    
                    await self.websocket.send(json.dumps(message))
                    frames_sent += frames_to_read
                    
                    # Progress indicator
                    progress = (frames_sent / total_frames) * 100
                    print(f"\rSending audio: {progress:.1f}%", end="", flush=True)
                    
                    # Small delay to simulate real-time streaming
                    await asyncio.sleep(0.1)
                
                print()  # New line after progress
                logger.info("Audio sent successfully, waiting for response...")
                
                # Wait for response
                response = await asyncio.wait_for(self.websocket.recv(), timeout=15.0)
                response_data = json.loads(response)
                
                return response_data
                
        except asyncio.TimeoutError:
            return {"error": "Timeout waiting for response"}
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            return {"error": str(e)}
    
    def save_audio_response(self, audio_base64: str, output_file: str):
        """Save audio response to file."""
        try:
            audio_data = base64.b64decode(audio_base64)
            
            with wave.open(output_file, 'wb') as wf:
                wf.setnchannels(1)  # Mono
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(config.SAMPLE_RATE)  # 16kHz
                wf.writeframes(audio_data)
            
            logger.info(f"Audio response saved to: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving audio: {e}")
            return False

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Policybazaar Voice Bot with WAV files")
    parser.add_argument("wav_file", help="Path to WAV file to process")
    parser.add_argument("--output", "-o", help="Output file for audio response", 
                       default="response.wav")
    parser.add_argument("--chunk-size", "-c", type=int, default=1024,
                       help="Chunk size for streaming (default: 1024)")
    parser.add_argument("--play", "-p", action="store_true",
                       help="Play the audio response after saving")
    
    args = parser.parse_args()
    
    print("🎵 Policybazaar Voice Bot - WAV File Tester")
    print("=" * 50)
    
    tester = WAVFileTester()
    
    try:
        # Connect to voice bot
        print("Connecting to voice bot server...")
        if not await tester.connect():
            print("❌ Failed to connect to voice bot server")
            print("Make sure voice_bot_server.py is running")
            return
        
        print("✅ Connected successfully")
        
        # Process WAV file
        print(f"Processing file: {args.wav_file}")
        result = await tester.process_wav_file(args.wav_file, args.chunk_size)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print("✅ Response received!")
            
            # Display text response
            if "text" in result:
                print(f"\n💬 AI Response: {result['text']}")
            
            # Save audio response
            if "audio" in result:
                if tester.save_audio_response(result["audio"], args.output):
                    print(f"🔊 Audio saved to: {args.output}")
                    
                    # Play audio if requested
                    if args.play:
                        try:
                            import subprocess
                            import platform
                            
                            system = platform.system()
                            if system == "Linux":
                                subprocess.run(["aplay", args.output])
                            elif system == "Darwin":  # macOS
                                subprocess.run(["afplay", args.output])
                            elif system == "Windows":
                                subprocess.run(["start", args.output], shell=True)
                            else:
                                print("⚠️  Auto-play not supported on this system")
                                
                        except Exception as e:
                            print(f"⚠️  Could not play audio: {e}")
            else:
                print("⚠️  No audio in response")
        
        # Disconnect
        await tester.disconnect()
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await tester.disconnect()

def create_sample_wav():
    """Create a sample WAV file for testing."""
    import numpy as np
    
    print("Creating sample WAV file for testing...")
    
    # Generate a simple spoken-like audio pattern
    duration = 3.0  # seconds
    sample_rate = config.SAMPLE_RATE
    
    # Create a more complex waveform that sounds like speech
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Mix multiple frequencies to simulate speech
    frequencies = [200, 400, 800, 1200]  # Typical speech frequencies
    audio = np.zeros_like(t)
    
    for i, freq in enumerate(frequencies):
        # Add some variation to make it more speech-like
        amplitude = 0.1 * (1 + 0.5 * np.sin(2 * np.pi * 0.5 * t))  # Amplitude modulation
        audio += amplitude * np.sin(2 * np.pi * freq * t) * np.exp(-t * 0.1)  # Decay
    
    # Add some noise to make it more realistic
    noise = np.random.normal(0, 0.01, len(audio))
    audio += noise
    
    # Normalize
    audio = audio / np.max(np.abs(audio)) * 0.7
    
    # Convert to 16-bit integers
    audio_data = (audio * 32767).astype(np.int16)
    
    # Save as WAV file
    with wave.open("sample_test.wav", 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data.tobytes())
    
    print("✅ Created sample_test.wav")
    print("You can test with: python wav_file_tester.py sample_test.wav")

if __name__ == "__main__":
    if len(os.sys.argv) == 1:
        print("Usage: python wav_file_tester.py <wav_file> [options]")
        print("\nTo create a sample WAV file for testing:")
        print("python -c \"from wav_file_tester import create_sample_wav; create_sample_wav()\"")
        print("\nExample:")
        print("python wav_file_tester.py sample_test.wav --output my_response.wav --play")
    else:
        asyncio.run(main())
