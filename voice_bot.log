2025-07-28 13:06:44,065 - voice_bot - INFO - start_server:232 - Starting voice bot server on localhost:8765
2025-07-28 13:07:45,534 - voice_bot - INFO - start_server:232 - Starting voice bot server on localhost:8765
2025-07-28 13:07:45,541 - voice_bot - INFO - start_server:251 - Voice bot server is running...
2025-07-28 13:08:03,146 - voice_bot - INFO - log_call_start:60 - New call started - Client: {'address': "('127.0.0.1', 44768)", 'timestamp': 1753688283.146126}
2025-07-28 13:08:03,146 - voice_bot - ERROR - log_speech_recognition_error:80 - Speech recognition error: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'
2025-07-28 13:08:03,146 - voice_bot - DEBUG - log_speech_recognition_error:81 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

2025-07-28 13:08:03,564 - voice_bot - ERROR - log_tts_error:86 - TTS error: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]
2025-07-28 13:08:03,575 - voice_bot - DEBUG - log_tts_error:87 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1181, in __call__
    return _end_unary_response_blocking(state, call, False, None)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:**************:443 {grpc_status:7, grpc_message:"Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 207, in send_audio_response
    response = tts_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/cloud/texttospeech_v1/services/text_to_speech/client.py", line 893, in synthesize_speech
    response = rpc(
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.PermissionDenied: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]

2025-07-28 13:08:20,599 - voice_bot - INFO - handle_client:106 - Client disconnected
2025-07-28 13:08:20,599 - voice_bot - INFO - log_call_end:66 - Call completed successfully - Duration: 17.45s
2025-07-28 13:12:13,793 - voice_bot - INFO - start_server:232 - Starting voice bot server on localhost:8765
2025-07-28 13:12:13,800 - voice_bot - INFO - start_server:251 - Voice bot server is running...
2025-07-28 13:14:03,501 - voice_bot - INFO - log_call_start:60 - New call started - Client: {'address': "('127.0.0.1', 49152)", 'timestamp': 1753688643.5016024}
2025-07-28 13:14:03,501 - voice_bot - ERROR - log_speech_recognition_error:80 - Speech recognition error: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'
2025-07-28 13:14:03,502 - voice_bot - DEBUG - log_speech_recognition_error:81 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

2025-07-28 13:14:04,186 - voice_bot - ERROR - log_tts_error:86 - TTS error: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]
2025-07-28 13:14:04,189 - voice_bot - DEBUG - log_tts_error:87 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1181, in __call__
    return _end_unary_response_blocking(state, call, False, None)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:***************:443 {grpc_status:7, grpc_message:"Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 207, in send_audio_response
    response = tts_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/cloud/texttospeech_v1/services/text_to_speech/client.py", line 893, in synthesize_speech
    response = rpc(
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.PermissionDenied: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]

2025-07-28 13:14:09,886 - voice_bot - INFO - log_call_start:60 - New call started - Client: {'address': "('127.0.0.1', 33792)", 'timestamp': 1753688649.8862987}
2025-07-28 13:14:09,887 - voice_bot - ERROR - log_speech_recognition_error:80 - Speech recognition error: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'
2025-07-28 13:14:09,887 - voice_bot - DEBUG - log_speech_recognition_error:81 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

2025-07-28 13:14:10,185 - voice_bot - ERROR - log_tts_error:86 - TTS error: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]
2025-07-28 13:14:10,185 - voice_bot - DEBUG - log_tts_error:87 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1181, in __call__
    return _end_unary_response_blocking(state, call, False, None)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:***************:443 {grpc_status:7, grpc_message:"Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 207, in send_audio_response
    response = tts_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/cloud/texttospeech_v1/services/text_to_speech/client.py", line 893, in synthesize_speech
    response = rpc(
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.PermissionDenied: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]

2025-07-28 13:14:43,521 - voice_bot - INFO - handle_client:106 - Client disconnected
2025-07-28 13:14:43,521 - voice_bot - INFO - log_call_end:66 - Call completed successfully - Duration: 40.02s
2025-07-28 13:14:49,897 - voice_bot - INFO - handle_client:106 - Client disconnected
2025-07-28 13:14:49,897 - voice_bot - INFO - log_call_end:66 - Call completed successfully - Duration: 40.01s
2025-07-28 13:15:45,347 - voice_bot - INFO - log_call_start:60 - New call started - Client: {'address': "('127.0.0.1', 37162)", 'timestamp': 1753688745.3472266}
2025-07-28 13:15:45,356 - voice_bot - ERROR - call:201 - Circuit breaker opened after 3 failures
2025-07-28 13:15:45,356 - voice_bot - ERROR - log_speech_recognition_error:80 - Speech recognition error: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'
2025-07-28 13:15:45,356 - voice_bot - DEBUG - log_speech_recognition_error:81 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

2025-07-28 13:15:45,492 - voice_bot - ERROR - call:201 - Circuit breaker opened after 3 failures
2025-07-28 13:15:45,493 - voice_bot - ERROR - log_tts_error:86 - TTS error: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]
2025-07-28 13:15:45,497 - voice_bot - DEBUG - log_tts_error:87 - Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 139, in stream_recognition
    responses = speech_recognition_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
TypeError: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1181, in __call__
    return _end_unary_response_blocking(state, call, False, None)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.PERMISSION_DENIED
	details = "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
	debug_error_string = "UNKNOWN:Error received from peer ipv4:***************:443 {grpc_message:"Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.", grpc_status:7}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Projects/voice-bot3/voice_bot_server.py", line 207, in send_audio_response
    response = tts_breaker.call(
  File "/home/<USER>/Projects/voice-bot3/error_handler.py", line 185, in call
    result = func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/cloud/texttospeech_v1/services/text_to_speech/client.py", line 893, in synthesize_speech
    response = rpc(
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/home/<USER>/.pyenv/versions/3.10.15/lib/python3.10/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.PermissionDenied: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]

2025-07-28 13:16:25,390 - voice_bot - INFO - handle_client:106 - Client disconnected
2025-07-28 13:16:25,390 - voice_bot - INFO - log_call_end:66 - Call completed successfully - Duration: 40.03s
2025-07-28 13:17:13,848 - voice_bot - INFO - log_metrics:107 - Voice Bot Metrics: {
  "total_calls": 3,
  "successful_calls": 3,
  "failed_calls": 0,
  "avg_response_time": 40.02189437548319,
  "total_response_time": 120.06568312644958,
  "speech_recognition_errors": 3,
  "tts_errors": 3,
  "ai_generation_errors": 0,
  "websocket_errors": 0
}
2025-07-28 13:22:13,948 - voice_bot - INFO - log_metrics:107 - Voice Bot Metrics: {
  "total_calls": 3,
  "successful_calls": 3,
  "failed_calls": 0,
  "avg_response_time": 40.02189437548319,
  "total_response_time": 120.06568312644958,
  "speech_recognition_errors": 3,
  "tts_errors": 3,
  "ai_generation_errors": 0,
  "websocket_errors": 0
}
