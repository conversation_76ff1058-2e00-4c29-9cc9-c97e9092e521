#!/usr/bin/env python3
"""
Quick fix script for common voice bot issues
"""

import os
import json
import subprocess
import webbrowser

def main():
    print("🔧 Quick Fix for Policybazaar Voice Bot Issues")
    print("=" * 50)
    
    # Check credentials file
    if not os.path.exists("matrixteam.json"):
        print("❌ matrixteam.json not found!")
        return
    
    # Read project info
    with open("matrixteam.json", "r") as f:
        creds = json.load(f)
    
    project_id = creds.get("project_id", "unknown")
    print(f"📋 Project ID: {project_id}")
    
    # The main issue is APIs not enabled
    print("\n🚨 MAIN ISSUE: Google Cloud APIs are not enabled")
    print("\n🔧 SOLUTION: Enable the required APIs manually")
    
    apis_to_enable = [
        ("Speech-to-Text API", f"https://console.developers.google.com/apis/api/speech.googleapis.com/overview?project={project_id}"),
        ("Text-to-Speech API", f"https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project={project_id}"),
        ("Vertex AI API", f"https://console.developers.google.com/apis/api/aiplatform.googleapis.com/overview?project={project_id}")
    ]
    
    print("\n📋 Please enable these APIs by clicking the links:")
    for api_name, url in apis_to_enable:
        print(f"\n🔗 {api_name}:")
        print(f"   {url}")
    
    # Ask user if they want to open the links
    try:
        response = input("\n❓ Do you want me to open these links in your browser? (y/n): ").lower()
        if response == 'y':
            for api_name, url in apis_to_enable:
                print(f"🌐 Opening {api_name}...")
                webbrowser.open(url)
                input("   Press Enter after enabling this API...")
    except KeyboardInterrupt:
        print("\n🛑 Cancelled by user")
    
    print("\n✅ After enabling all APIs:")
    print("1. Wait 2-3 minutes for APIs to propagate")
    print("2. Restart your voice bot server: python voice_bot_server.py")
    print("3. Test with: python web_test_client.py")
    
    print("\n🎯 Quick Test Commands:")
    print("# Terminal 1:")
    print("python voice_bot_server.py")
    print("\n# Terminal 2:")
    print("python web_test_client.py")
    print("\n# Browser:")
    print("http://localhost:8080")

if __name__ == "__main__":
    main()
