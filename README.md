# Policybazaar Voice Bot

A real-time voice bot system for Policybazaar that handles customer calls with minimal latency using Google Cloud services.

## Architecture

The system uses a streaming architecture for optimal performance:

1. **WebSocket Server**: Receives audio chunks from dialer team
2. **Google Speech-to-Text Streaming**: Converts audio to text in real-time
3. **Vertex AI**: Generates intelligent responses using Gemini model
4. **Google Text-to-Speech Streaming**: Converts responses back to audio
5. **Circuit Breakers**: Handles service failures gracefully

## Features

- ✅ Real-time audio streaming via WebSocket
- ✅ Low-latency speech recognition and synthesis
- ✅ AI-powered conversation handling
- ✅ Comprehensive error handling and logging
- ✅ Circuit breaker pattern for service reliability
- ✅ Metrics tracking and monitoring
- ✅ Test client for development and testing

## Prerequisites

1. **Google Cloud Project** with the following APIs enabled:
   - Speech-to-Text API
   - Text-to-Speech API
   - Vertex AI API

2. **Service Account** with permissions for:
   - Speech Client
   - Text-to-Speech Client
   - Vertex AI User

3. **Python 3.8+**

## Quick Start

### 1. Setup

```bash
# Clone and navigate to the project
cd voice-bot3

# Run the setup script
python setup.py
```

The setup script will:
- Install required Python packages
- Validate your Google credentials
- Check audio system compatibility
- Update configuration with your project ID

### 2. Configuration

Update `config.py` with your specific settings:

```python
PROJECT_ID = "your-actual-project-id"  # Auto-updated by setup.py
VERTEX_AI_LOCATION = "us-central1"     # Change if needed
TTS_VOICE_NAME = "en-IN-Wavenet-A"     # Indian English voice
```

### 3. Start the Server

```bash
python voice_bot_server.py
```

### 4. Test the System

In another terminal:

```bash
python test_client.py
```

## Usage

### For Dialer Team Integration

The voice bot server expects WebSocket connections with JSON messages:

```json
{
  "type": "audio",
  "audio": "base64_encoded_audio_data"
}
```

Response format:

```json
{
  "type": "audio_response",
  "audio": "base64_encoded_audio_response",
  "text": "AI response text"
}
```

### Audio Format Requirements

- **Encoding**: LINEAR16 (16-bit PCM)
- **Sample Rate**: 16,000 Hz
- **Channels**: 1 (mono)
- **Chunk Size**: 1024 frames

### Test Client Commands

- `s` - Start recording from microphone
- `e` - Stop recording
- `f <filename>` - Send audio file
- `q` - Quit

## File Structure

```
voice-bot3/
├── voice_bot_server.py    # Main WebSocket server
├── test_client.py         # Test client for simulation
├── config.py             # Configuration settings
├── error_handler.py      # Error handling and logging
├── setup.py             # Setup and validation script
├── requirements.txt     # Python dependencies
├── matrixteam.json      # Google service account credentials
└── README.md           # This file
```

## Monitoring and Logging

### Log Files

- `voice_bot.log` - General application logs
- `voice_bot_errors.log` - Error-specific logs

### Metrics Tracked

- Total calls handled
- Success/failure rates
- Average response times
- Service-specific error counts
- Circuit breaker states

### Viewing Metrics

Metrics are logged every 5 minutes. You can also access them programmatically:

```python
from error_handler import voice_bot_logger
metrics = voice_bot_logger.get_metrics()
```

## Performance Optimization

### Latency Reduction Techniques

1. **Streaming Processing**: Audio is processed as it arrives
2. **Concurrent Operations**: Speech recognition and AI generation run in parallel
3. **Circuit Breakers**: Prevent cascading failures
4. **Connection Pooling**: Reuse Google Cloud client connections
5. **Optimized Audio Format**: 16kHz mono for minimal bandwidth

### Expected Latency

- **Speech Recognition**: ~100-300ms
- **AI Response Generation**: ~200-500ms
- **Text-to-Speech**: ~100-200ms
- **Total End-to-End**: ~400-1000ms

## Production Deployment

### Security Considerations

1. **SSL/TLS**: Use secure WebSocket connections (wss://)
2. **Authentication**: Implement proper client authentication
3. **Rate Limiting**: Prevent abuse and manage resources
4. **Network Security**: Use VPC and firewall rules

### Scaling

1. **Horizontal Scaling**: Deploy multiple server instances
2. **Load Balancing**: Distribute connections across instances
3. **Resource Monitoring**: Monitor CPU, memory, and network usage
4. **Auto-scaling**: Scale based on connection count and latency

### Environment Variables

For production, use environment variables instead of hardcoded values:

```bash
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/credentials.json"
export PROJECT_ID="your-project-id"
export VOICE_BOT_HOST="0.0.0.0"
export VOICE_BOT_PORT="8765"
```

## Troubleshooting

### Common Issues

1. **Audio Quality Issues**
   - Ensure 16kHz, 16-bit, mono format
   - Check network bandwidth
   - Verify microphone settings

2. **High Latency**
   - Check Google Cloud region settings
   - Monitor network connectivity
   - Review circuit breaker logs

3. **Authentication Errors**
   - Verify service account permissions
   - Check credentials file path
   - Ensure APIs are enabled

### Debug Mode

Enable debug logging:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Support

For issues and questions:
1. Check the logs in `voice_bot.log` and `voice_bot_errors.log`
2. Review the metrics for performance insights
3. Test with the provided test client
4. Verify Google Cloud service status

## License

Internal use for Policybazaar projects.
