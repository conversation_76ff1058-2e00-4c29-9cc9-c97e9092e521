# 🔊 How to Re-enable Text-to-Speech (TTS)

## When DevOps Team Adds TTS Permissions

Once your DevOps team adds the **"Cloud Text-to-Speech Client"** role to your service account, follow these steps to re-enable audio responses:

### 1. **Uncomment TTS Client Initialization**

In `voice_bot_server.py`, line 25:
```python
# Change this:
# self.tts_client = texttospeech.TextToSpeechClient(credentials=self.credentials)

# To this:
self.tts_client = texttospeech.TextToSpeechClient(credentials=self.credentials)
```

### 2. **Uncomment TTS Configuration**

In `voice_bot_server.py`, lines 45-53:
```python
# Change this:
# self.tts_voice = texttospeech.VoiceSelectionParams(
#     language_code=config.TTS_LANGUAGE_CODE,
#     name=config.TTS_VOICE_NAME
# )
# 
# self.tts_audio_config = texttospeech.AudioConfig(
#     audio_encoding=texttospeech.AudioEncoding.LINEAR16,
#     sample_rate_hertz=config.SAMPLE_RATE
# )

# To this:
self.tts_voice = texttospeech.VoiceSelectionParams(
    language_code=config.TTS_LANGUAGE_CODE,
    name=config.TTS_VOICE_NAME
)

self.tts_audio_config = texttospeech.AudioConfig(
    audio_encoding=texttospeech.AudioEncoding.LINEAR16,
    sample_rate_hertz=config.SAMPLE_RATE
)
```

### 3. **Re-enable TTS Method**

In `voice_bot_server.py`, lines 220-251:
```python
# Change this:
# @handle_exceptions("tts")
async def send_audio_response(self, websocket, text_response: str):
    # TTS temporarily disabled - uncomment when permissions are added
    # try:
    #     # Generate speech with circuit breaker
    #     synthesis_input = texttospeech.SynthesisInput(text=text_response)
    # 
    #     response = tts_breaker.call(
    #         self.tts_client.synthesize_speech,
    #         input=synthesis_input,
    #         voice=self.tts_voice,
    #         audio_config=self.tts_audio_config
    #     )
    #     
    #     # Encode audio as base64
    #     audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')
    #     
    #     # Send response back to client
    #     response_data = {
    #         "type": "audio_response",
    #         "audio": audio_base64,
    #         "text": text_response
    #     }
    #     
    #     await websocket.send(json.dumps(response_data))
    #     voice_bot_logger.logger.info(f"Sent audio response: {text_response}")
    # 
    # except Exception as e:
    #     voice_bot_logger.log_tts_error(e)
    
    # For now, send text response instead
    await self.send_text_response(websocket, text_response, "")

# To this:
@handle_exceptions("tts")
async def send_audio_response(self, websocket, text_response: str):
    """Convert text to speech and send audio response."""
    try:
        # Generate speech with circuit breaker
        synthesis_input = texttospeech.SynthesisInput(text=text_response)

        response = tts_breaker.call(
            self.tts_client.synthesize_speech,
            input=synthesis_input,
            voice=self.tts_voice,
            audio_config=self.tts_audio_config
        )
        
        # Encode audio as base64
        audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')
        
        # Send response back to client
        response_data = {
            "type": "audio_response",
            "audio": audio_base64,
            "text": text_response
        }
        
        await websocket.send(json.dumps(response_data))
        voice_bot_logger.logger.info(f"Sent audio response: {text_response}")

    except Exception as e:
        voice_bot_logger.log_tts_error(e)
```

### 4. **Switch Back to Audio Responses**

In `voice_bot_server.py`, line 162:
```python
# Change this:
await self.send_text_response(websocket, ai_response, transcript)

# To this:
await self.send_audio_response(websocket, ai_response)
```

And line 168:
```python
# Change this:
await self.send_text_response(websocket, "I'm sorry, I didn't catch that. Could you please repeat?", "")

# To this:
await self.send_audio_response(websocket, "I'm sorry, I didn't catch that. Could you please repeat?")
```

### 5. **Update Web Interface**

In `web_test_client.py`, update the HTML title:
```html
<!-- Change this: -->
<h3>AI Response: <small style="color: #666;">(Text only - Audio coming soon!)</small></h3>

<!-- To this: -->
<h3>AI Response:</h3>
```

### 6. **Test Everything**

```bash
# Restart the server
python voice_bot_server.py

# Test with web client
python web_test_client.py

# Test with WAV files
python wav_file_tester.py sample_test.wav --play
```

## 🎯 **Quick Enable Script**

You can also create a script to do this automatically:

```bash
# Create enable_tts.py
python -c "
import re

# Read and update voice_bot_server.py
with open('voice_bot_server.py', 'r') as f:
    content = f.read()

# Uncomment TTS lines
content = re.sub(r'# (self\.tts_client.*)', r'\1', content)
content = re.sub(r'# (self\.tts_voice.*)', r'\1', content, flags=re.DOTALL)
content = re.sub(r'# (@handle_exceptions.*)', r'\1', content)

# Write back
with open('voice_bot_server.py', 'w') as f:
    f.write(content)

print('✅ TTS re-enabled! Restart your server.')
"
```

That's it! Your voice bot will then return audio responses instead of text responses.
