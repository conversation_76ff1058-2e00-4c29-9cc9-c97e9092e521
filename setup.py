#!/usr/bin/env python3
"""
Setup script for Policybazaar Voice Bot
"""

import subprocess
import sys
import os
import json

def install_requirements():
    """Install required packages."""
    print("Installing Python packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python packages installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False
    return True

def check_google_credentials():
    """Check if Google credentials file exists and is valid."""
    if not os.path.exists("matrixteam.json"):
        print("❌ Google credentials file 'matrixteam.json' not found")
        print("Please ensure your Google service account JSON file is named 'matrixteam.json'")
        return False
    
    try:
        with open("matrixteam.json", 'r') as f:
            creds = json.load(f)
            
        required_fields = ["type", "project_id", "private_key_id", "private_key", "client_email"]
        missing_fields = [field for field in required_fields if field not in creds]
        
        if missing_fields:
            print(f"❌ Missing fields in credentials: {missing_fields}")
            return False
            
        print("✅ Google credentials file is valid")
        
        # Update config with project ID
        project_id = creds.get("project_id")
        if project_id:
            update_config_project_id(project_id)
            
        return True
        
    except json.JSONDecodeError:
        print("❌ Invalid JSON in credentials file")
        return False
    except Exception as e:
        print(f"❌ Error reading credentials: {e}")
        return False

def update_config_project_id(project_id):
    """Update the project ID in config.py."""
    try:
        with open("config.py", 'r') as f:
            content = f.read()
        
        # Replace the placeholder project ID
        content = content.replace(
            'PROJECT_ID = "your-project-id"',
            f'PROJECT_ID = "{project_id}"'
        )
        
        with open("config.py", 'w') as f:
            f.write(content)
            
        print(f"✅ Updated config.py with project ID: {project_id}")
        
    except Exception as e:
        print(f"⚠️  Could not update config.py: {e}")

def check_audio_system():
    """Check if audio system is available."""
    try:
        import pyaudio
        audio = pyaudio.PyAudio()
        
        # Check for input devices
        input_devices = []
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                input_devices.append(info['name'])
        
        # Check for output devices
        output_devices = []
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            if info['maxOutputChannels'] > 0:
                output_devices.append(info['name'])
        
        audio.terminate()
        
        if input_devices and output_devices:
            print("✅ Audio system is working")
            print(f"   Input devices: {len(input_devices)}")
            print(f"   Output devices: {len(output_devices)}")
            return True
        else:
            print("❌ No audio devices found")
            return False
            
    except Exception as e:
        print(f"❌ Audio system error: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Policybazaar Voice Bot...")
    print("=" * 50)
    
    success = True
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Check Google credentials
    if not check_google_credentials():
        success = False
    
    # Check audio system
    if not check_audio_system():
        success = False
        print("⚠️  Audio issues may affect testing with microphone")
    
    print("=" * 50)
    
    if success:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the voice bot server: python voice_bot_server.py")
        print("2. In another terminal, run the test client: python test_client.py")
        print("\nFor production deployment:")
        print("- Update config.py with your specific settings")
        print("- Configure proper logging and monitoring")
        print("- Set up SSL/TLS for secure WebSocket connections")
    else:
        print("❌ Setup completed with errors")
        print("Please fix the issues above before running the voice bot")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
