import asyncio
import websockets
import json
import base64
import pyaudio
import wave
import threading
import logging
from typing import Optional
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceBotTestClient:
    def __init__(self):
        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.audio = pyaudio.PyAudio()
        self.is_recording = False
        self.is_playing = False
        
        # Audio stream for recording
        self.record_stream = None
        # Audio stream for playback
        self.play_stream = None
        
    async def connect(self):
        """Connect to the voice bot server."""
        try:
            uri = f"ws://{config.HOST}:{config.PORT}"
            self.websocket = await websockets.connect(uri)
            logger.info(f"Connected to voice bot server at {uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the server."""
        if self.websocket:
            await self.websocket.send(json.dumps({"type": "end_call"}))
            await self.websocket.close()
            logger.info("Disconnected from server")
    
    def start_recording(self):
        """Start recording audio from microphone."""
        if self.is_recording:
            return
            
        self.is_recording = True
        
        self.record_stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=config.SAMPLE_RATE,
            input=True,
            frames_per_buffer=config.CHUNK_SIZE
        )
        
        # Start recording in a separate thread
        self.record_thread = threading.Thread(target=self._record_audio)
        self.record_thread.daemon = True
        self.record_thread.start()
        
        logger.info("Started recording...")
    
    def stop_recording(self):
        """Stop recording audio."""
        if not self.is_recording:
            return
            
        self.is_recording = False
        
        if self.record_stream:
            self.record_stream.stop_stream()
            self.record_stream.close()
            self.record_stream = None
            
        logger.info("Stopped recording")
    
    def _record_audio(self):
        """Record audio and send to server."""
        while self.is_recording and self.record_stream:
            try:
                # Read audio data
                audio_data = self.record_stream.read(config.CHUNK_SIZE, exception_on_overflow=False)
                
                # Send to server
                if self.websocket and not self.websocket.closed:
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    message = {
                        "type": "audio",
                        "audio": audio_base64
                    }
                    
                    # Send message asynchronously
                    asyncio.run_coroutine_threadsafe(
                        self.websocket.send(json.dumps(message)),
                        asyncio.get_event_loop()
                    )
                    
            except Exception as e:
                logger.error(f"Error recording audio: {e}")
                break
    
    def play_audio(self, audio_data: bytes):
        """Play audio response."""
        try:
            if self.is_playing:
                return
                
            self.is_playing = True
            
            # Create playback stream
            play_stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=config.SAMPLE_RATE,
                output=True
            )
            
            # Play audio
            play_stream.write(audio_data)
            play_stream.stop_stream()
            play_stream.close()
            
            self.is_playing = False
            logger.info("Finished playing audio response")
            
        except Exception as e:
            logger.error(f"Error playing audio: {e}")
            self.is_playing = False
    
    async def listen_for_responses(self):
        """Listen for responses from the server."""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                
                if data.get("type") == "audio_response":
                    # Decode and play audio
                    audio_data = base64.b64decode(data["audio"])
                    text_response = data.get("text", "")
                    
                    logger.info(f"Received response: {text_response}")
                    
                    # Play audio in a separate thread
                    play_thread = threading.Thread(
                        target=self.play_audio,
                        args=(audio_data,)
                    )
                    play_thread.daemon = True
                    play_thread.start()
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("Connection closed by server")
        except Exception as e:
            logger.error(f"Error listening for responses: {e}")
    
    async def simulate_call(self):
        """Simulate a phone call interaction."""
        if not await self.connect():
            return
        
        try:
            # Start listening for responses
            listen_task = asyncio.create_task(self.listen_for_responses())
            
            print("\n=== Voice Bot Test Client ===")
            print("Commands:")
            print("  's' - Start recording")
            print("  'e' - Stop recording")
            print("  'q' - Quit")
            print("  'f <filename>' - Send audio file")
            print("\nPress Enter after each command...")
            
            while True:
                command = input("\nEnter command: ").strip().lower()
                
                if command == 's':
                    self.start_recording()
                elif command == 'e':
                    self.stop_recording()
                elif command == 'q':
                    break
                elif command.startswith('f '):
                    filename = command[2:].strip()
                    await self.send_audio_file(filename)
                else:
                    print("Invalid command")
            
            # Cleanup
            self.stop_recording()
            listen_task.cancel()
            await self.disconnect()
            
        except KeyboardInterrupt:
            logger.info("Test interrupted by user")
        except Exception as e:
            logger.error(f"Error in simulation: {e}")
        finally:
            self.cleanup()
    
    async def send_audio_file(self, filename: str):
        """Send an audio file to the server."""
        try:
            with wave.open(filename, 'rb') as wf:
                # Read audio data in chunks
                while True:
                    audio_data = wf.readframes(config.CHUNK_SIZE)
                    if not audio_data:
                        break
                    
                    # Send to server
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    message = {
                        "type": "audio",
                        "audio": audio_base64
                    }
                    
                    await self.websocket.send(json.dumps(message))
                    
                    # Small delay to simulate real-time streaming
                    await asyncio.sleep(0.1)
                    
            logger.info(f"Sent audio file: {filename}")
            
        except FileNotFoundError:
            logger.error(f"Audio file not found: {filename}")
        except Exception as e:
            logger.error(f"Error sending audio file: {e}")
    
    def cleanup(self):
        """Clean up audio resources."""
        self.stop_recording()
        
        if self.audio:
            self.audio.terminate()

# Simple audio file generator for testing
def create_test_audio_file():
    """Create a simple test audio file."""
    import numpy as np
    
    # Generate a simple sine wave
    duration = 3  # seconds
    sample_rate = config.SAMPLE_RATE
    frequency = 440  # A4 note
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
    
    # Convert to 16-bit integers
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Save as WAV file
    with wave.open("test_audio.wav", 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data.tobytes())
    
    logger.info("Created test_audio.wav")

if __name__ == "__main__":
    # Create test audio file if it doesn't exist
    import os
    if not os.path.exists("test_audio.wav"):
        create_test_audio_file()
    
    client = VoiceBotTestClient()
    try:
        asyncio.run(client.simulate_call())
    except KeyboardInterrupt:
        logger.info("Test client stopped")
    finally:
        client.cleanup()
