#!/usr/bin/env python3
"""
Script to enable required Google Cloud APIs for Policybazaar Voice Bot
"""

import subprocess
import sys
import json
import config

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_gcloud_auth():
    """Check if gcloud is authenticated."""
    success, stdout, stderr = run_command("gcloud auth list --format='value(account)'")
    if success and stdout.strip():
        print(f"✅ Authenticated as: {stdout.strip()}")
        return True
    else:
        print("❌ Not authenticated with gcloud")
        return False

def get_project_info():
    """Get project information."""
    # Get project ID from config
    project_id = config.PROJECT_ID
    
    # Get project number
    success, stdout, stderr = run_command(f"gcloud projects describe {project_id} --format='value(projectNumber)'")
    if success:
        project_number = stdout.strip()
        print(f"📋 Project ID: {project_id}")
        print(f"📋 Project Number: {project_number}")
        return project_id, project_number
    else:
        print(f"❌ Could not get project info: {stderr}")
        return project_id, None

def enable_api(api_name, project_id):
    """Enable a specific API."""
    print(f"🔄 Enabling {api_name}...")
    success, stdout, stderr = run_command(f"gcloud services enable {api_name} --project={project_id}")
    
    if success:
        print(f"✅ {api_name} enabled successfully")
        return True
    else:
        print(f"❌ Failed to enable {api_name}: {stderr}")
        return False

def check_api_status(api_name, project_id):
    """Check if an API is enabled."""
    success, stdout, stderr = run_command(f"gcloud services list --enabled --filter='name:{api_name}' --project={project_id}")
    
    if success and api_name in stdout:
        print(f"✅ {api_name} is enabled")
        return True
    else:
        print(f"❌ {api_name} is not enabled")
        return False

def main():
    """Main function."""
    print("🚀 Google Cloud APIs Setup for Policybazaar Voice Bot")
    print("=" * 60)
    
    # Required APIs
    required_apis = [
        "speech.googleapis.com",
        "texttospeech.googleapis.com", 
        "aiplatform.googleapis.com"
    ]
    
    # Check gcloud authentication
    if not check_gcloud_auth():
        print("\n🔧 Please authenticate with gcloud first:")
        print("   gcloud auth login")
        print("   gcloud auth application-default login")
        return
    
    # Get project information
    project_id, project_number = get_project_info()
    if not project_number:
        print("\n❌ Could not retrieve project information")
        return
    
    print(f"\n📋 Working with project: {project_id} ({project_number})")
    
    # Set the project
    print(f"\n🔧 Setting active project...")
    success, stdout, stderr = run_command(f"gcloud config set project {project_id}")
    if not success:
        print(f"❌ Failed to set project: {stderr}")
        return
    
    print("\n🔍 Checking API status...")
    
    # Check current status
    apis_to_enable = []
    for api in required_apis:
        if not check_api_status(api, project_id):
            apis_to_enable.append(api)
    
    if not apis_to_enable:
        print("\n🎉 All required APIs are already enabled!")
        return
    
    # Enable missing APIs
    print(f"\n🔄 Enabling {len(apis_to_enable)} APIs...")
    
    failed_apis = []
    for api in apis_to_enable:
        if not enable_api(api, project_id):
            failed_apis.append(api)
    
    if failed_apis:
        print(f"\n❌ Failed to enable {len(failed_apis)} APIs:")
        for api in failed_apis:
            print(f"   - {api}")
        print("\n🔧 Manual steps:")
        print("1. Go to Google Cloud Console")
        print("2. Select your project")
        print("3. Go to APIs & Services > Library")
        print("4. Search and enable each API manually")
    else:
        print("\n🎉 All APIs enabled successfully!")
        print("\n⏳ Please wait 2-3 minutes for the APIs to propagate")
        print("   Then restart your voice bot server")
    
    # Show direct links
    print(f"\n🔗 Direct links to enable APIs:")
    base_url = "https://console.developers.google.com/apis/api"
    for api in required_apis:
        api_name = api.replace('.googleapis.com', '')
        print(f"   {api_name}: {base_url}/{api}/overview?project={project_id}")

if __name__ == "__main__":
    main()
