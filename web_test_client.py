#!/usr/bin/env python3
"""
Web-based test client for Policybazaar Voice Bot
Simple HTML interface for recording and testing
"""

import asyncio
import websockets
import json
import base64
import logging
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import time
import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebTestServer:
    def __init__(self):
        self.websocket = None
        self.connected = False
        
    async def connect_to_voice_bot(self):
        """Connect to the voice bot server."""
        try:
            uri = f"ws://{config.HOST}:{config.PORT}"
            self.websocket = await websockets.connect(uri)
            self.connected = True
            logger.info(f"Connected to voice bot at {uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to voice bot: {e}")
            return False
    
    async def send_audio_data(self, audio_data: bytes):
        """Send audio data to voice bot."""
        if not self.websocket or not self.connected:
            return {"error": "Not connected to voice bot"}
        
        try:
            # Encode audio as base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            message = {
                "type": "audio",
                "audio": audio_base64
            }
            
            # Send audio data
            await self.websocket.send(json.dumps(message))
            
            # Wait for response
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            response_data = json.loads(response)
            
            return response_data
            
        except asyncio.TimeoutError:
            return {"error": "Timeout waiting for response"}
        except Exception as e:
            logger.error(f"Error sending audio: {e}")
            return {"error": str(e)}
    
    async def disconnect(self):
        """Disconnect from voice bot."""
        if self.websocket and self.connected:
            try:
                await self.websocket.send(json.dumps({"type": "end_call"}))
                await self.websocket.close()
            except:
                pass
            finally:
                self.connected = False
                logger.info("Disconnected from voice bot")

# Global test server instance
test_server = WebTestServer()

def create_html_interface():
    """Create the HTML interface file."""
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Policybazaar Voice Bot Tester</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.recording { background: #fff3cd; color: #856404; }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        button:hover { background: #0056b3; transform: translateY(-2px); }
        button:disabled { background: #6c757d; cursor: not-allowed; transform: none; }
        .record-btn {
            background: #dc3545;
            font-size: 18px;
            padding: 20px 40px;
        }
        .record-btn:hover { background: #c82333; }
        .record-btn.recording {
            background: #28a745;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .response-area {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            min-height: 100px;
        }
        .response-text {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
        }
        .file-upload {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #007bff;
            border-radius: 10px;
            text-align: center;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Policybazaar Voice Bot Tester</h1>
        
        <div id="status" class="status disconnected">
            Disconnected from Voice Bot
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">Connect to Voice Bot</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        
        <div class="controls">
            <button id="recordBtn" class="record-btn" onclick="toggleRecording()" disabled>
                🎤 Start Recording
            </button>
        </div>
        
        <div class="file-upload">
            <h3>Or Upload WAV File</h3>
            <input type="file" id="audioFile" accept=".wav" onchange="handleFileUpload()">
            <button onclick="processFile()" disabled id="processBtn">Process File</button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing audio...</p>
        </div>
        
        <div class="response-area">
            <h3>AI Response: <small style="color: #666;">(Text only - Audio coming soon!)</small></h3>
            <div id="responseText" class="response-text">
                Connect to the voice bot and start recording or upload a WAV file to test.<br>
                <em>Note: Currently returning text responses only while TTS permissions are being configured.</em>
            </div>
            <audio id="responseAudio" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        let isConnected = false;

        async function connect() {
            try {
                const response = await fetch('/connect', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    isConnected = true;
                    updateStatus('Connected to Voice Bot', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('recordBtn').disabled = false;
                } else {
                    updateStatus('Failed to connect: ' + result.error, 'disconnected');
                }
            } catch (error) {
                updateStatus('Connection error: ' + error.message, 'disconnected');
            }
        }

        async function disconnect() {
            try {
                await fetch('/disconnect', { method: 'POST' });
                isConnected = false;
                updateStatus('Disconnected from Voice Bot', 'disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('recordBtn').disabled = true;
            } catch (error) {
                console.error('Disconnect error:', error);
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: { 
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    await sendAudioToBot(audioBlob);
                };
                
                mediaRecorder.start();
                isRecording = true;
                
                updateStatus('Recording... Click stop when done', 'recording');
                document.getElementById('recordBtn').textContent = '⏹️ Stop Recording';
                document.getElementById('recordBtn').classList.add('recording');
                
            } catch (error) {
                alert('Error accessing microphone: ' + error.message);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
                
                updateStatus('Processing audio...', 'connected');
                document.getElementById('recordBtn').textContent = '🎤 Start Recording';
                document.getElementById('recordBtn').classList.remove('recording');
                
                showLoading(true);
            }
        }

        function handleFileUpload() {
            const fileInput = document.getElementById('audioFile');
            const processBtn = document.getElementById('processBtn');
            processBtn.disabled = !fileInput.files.length || !isConnected;
        }

        async function processFile() {
            const fileInput = document.getElementById('audioFile');
            if (!fileInput.files.length) return;
            
            const file = fileInput.files[0];
            showLoading(true);
            await sendAudioToBot(file);
        }

        async function sendAudioToBot(audioBlob) {
            try {
                const formData = new FormData();
                formData.append('audio', audioBlob);
                
                const response = await fetch('/process_audio', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showLoading(false);
                
                if (result.success) {
                    displayResponse(result.response);
                } else {
                    displayError(result.error);
                }
                
            } catch (error) {
                showLoading(false);
                displayError('Error processing audio: ' + error.message);
            }
        }

        function displayResponse(response) {
            const responseText = document.getElementById('responseText');
            const responseAudio = document.getElementById('responseAudio');

            // Display text response
            if (response.text) {
                responseText.innerHTML = `
                    <strong>AI Response:</strong><br>
                    ${response.text}<br><br>
                    ${response.user_input ? `<em>You said: "${response.user_input}"</em>` : ''}
                `;
            } else {
                responseText.textContent = 'No text response received';
            }

            // Handle audio if available (currently disabled)
            if (response.audio) {
                const audioBlob = base64ToBlob(response.audio, 'audio/wav');
                const audioUrl = URL.createObjectURL(audioBlob);
                responseAudio.src = audioUrl;
                responseAudio.style.display = 'block';
                responseAudio.play();
            } else {
                responseAudio.style.display = 'none';
            }
        }

        function displayError(error) {
            document.getElementById('responseText').textContent = 'Error: ' + error;
            document.getElementById('responseAudio').style.display = 'none';
        }

        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }

        // Auto-connect on page load
        window.onload = function() {
            // You can uncomment this to auto-connect
            // connect();
        };
    </script>
</body>
</html>"""
    
    with open("test_interface.html", "w") as f:
        f.write(html_content)
    
    logger.info("Created test_interface.html")

def create_web_server():
    """Create a simple web server to serve the interface and handle requests."""
    
    from http.server import BaseHTTPRequestHandler
    import urllib.parse
    import io
    import wave
    
    class TestHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == "/" or self.path == "/index.html":
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                
                with open("test_interface.html", "r") as f:
                    self.wfile.write(f.read().encode())
            else:
                self.send_response(404)
                self.end_headers()
        
        def do_POST(self):
            if self.path == "/connect":
                # Connect to voice bot
                try:
                    import threading
                    result = {"success": False, "error": "Connection failed"}

                    def connect_async():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            success = loop.run_until_complete(test_server.connect_to_voice_bot())
                            result["success"] = success
                            if not success:
                                result["error"] = "Could not connect to voice bot server"
                        except Exception as e:
                            result["error"] = str(e)
                        finally:
                            loop.close()

                    thread = threading.Thread(target=connect_async)
                    thread.start()
                    thread.join(timeout=10)  # 10 second timeout

                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(result).encode())

                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"success": False, "error": str(e)}).encode())

            elif self.path == "/disconnect":
                # Disconnect from voice bot
                try:
                    def disconnect_async():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(test_server.disconnect())
                        finally:
                            loop.close()

                    thread = threading.Thread(target=disconnect_async)
                    thread.start()
                    thread.join(timeout=5)

                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"success": True}).encode())

                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"success": False, "error": str(e)}).encode())
            
            elif self.path == "/process_audio":
                # Process audio file
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                # Parse multipart form data (simplified)
                boundary = self.headers['Content-Type'].split('boundary=')[1]
                parts = post_data.split(f'--{boundary}'.encode())
                
                audio_data = None
                for part in parts:
                    if b'Content-Disposition: form-data; name="audio"' in part:
                        # Extract audio data
                        data_start = part.find(b'\r\n\r\n') + 4
                        audio_data = part[data_start:]
                        break
                
                if audio_data:
                    # Process the audio
                    try:
                        result = {"success": False, "error": "Processing failed"}

                        def process_async():
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                response = loop.run_until_complete(test_server.send_audio_data(audio_data))
                                if "error" in response:
                                    result["error"] = response["error"]
                                else:
                                    result["success"] = True
                                    result["response"] = response
                            except Exception as e:
                                result["error"] = str(e)
                            finally:
                                loop.close()

                        thread = threading.Thread(target=process_async)
                        thread.start()
                        thread.join(timeout=30)  # 30 second timeout for audio processing

                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps(result).encode())

                    except Exception as e:
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps({"success": False, "error": str(e)}).encode())
                else:
                    self.send_response(400)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({"success": False, "error": "No audio data"}).encode())
    
    return TestHandler

def main():
    """Main function to start the web test client."""
    print("🌐 Starting Policybazaar Voice Bot Web Tester")
    print("=" * 50)
    
    # Create HTML interface
    create_html_interface()
    
    # Start web server
    handler = create_web_server()
    server = HTTPServer(('localhost', 8080), handler)
    
    print("✅ Web interface created: test_interface.html")
    print(f"🚀 Starting web server at http://localhost:8080")
    print("\nInstructions:")
    print("1. Make sure voice_bot_server.py is running")
    print("2. Open http://localhost:8080 in your browser")
    print("3. Click 'Connect to Voice Bot'")
    print("4. Either record audio or upload a WAV file")
    print("\nPress Ctrl+C to stop the server")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down web server...")
        server.shutdown()

if __name__ == "__main__":
    main()
