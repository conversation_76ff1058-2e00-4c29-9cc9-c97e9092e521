2025-07-28 13:08:03,146 - voice_bot - ERROR - log_speech_recognition_error:80 - Speech recognition error: SpeechHelpers.streaming_recognize() missing 1 required positional argument: 'config'
2025-07-28 13:08:03,564 - voice_bot - ERROR - log_tts_error:86 - TTS error: 403 Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "texttospeech.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Text-to-Speech API"
}
metadata {
  key: "containerInfo"
  value: "862644857848"
}
metadata {
  key: "consumer"
  value: "projects/862644857848"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
, locale: "en-US"
message: "Cloud Text-to-Speech API has not been used in project 862644857848 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/texttospeech.googleapis.com/overview?project=862644857848"
}
]
